# 《“Aegis”项目软件架构与技术栈设计》

文档版本: 2.0  
更新日期: 2025年8月2日  
目标读者: Aegis项目开发团队

---

### 目录

1.  [引言](#1-引言)
    1.1. [项目目标](#11-项目目标)
    1.2. [核心设计原则](#12-核心设计原则)
    1.3. [报告结构](#13-报告结构)
2.  [系统总体架构](#2-系统总体架构)
    2.1. [架构概览](#21-架构概览)
    2.2. [核心组件职责](#22-核心组件职责)
    2.3. [数据与控制流](#23-数据与控制流)
3.  [服务器端架构设计 (Node.js)](#3-服务器端架构设计-nodejs)
    3.1. [技术栈与目标](#31-技术栈与目标)
    3.2. [核心模块与交互](#32-核心模块与交互)
    3.3. [模块详解](#33-模块详解)
    3.4. [数据处理与存储](#34-数据处理与存储)
    3.5. [Operator API 接口定义](#35-operator-api-接口定义)
4.  [客户端架构设计 (Go)](#4-客户端架构设计-go)
    4.1. [设计哲学与目标](#41-设计哲学与目标)
    4.2. [启动与加载流程](#42-启动与加载流程)
    4.3. [插件的内存执行模型](#43-插件的内存执行模型)
    4.4. [核心运行时 API (CoreAPI)](#44-核心运行时-api-coreapi)
    4.5. [心跳与状态管理](#45-心跳与状态管理)
5.  [通信协议规范](#5-通信协议规范)
    5.1. [协议概述](#51-协议概述)
    5.2. [XOR 加密层](#52-xor-加密层)
    5.3. [通用消息结构](#53-通用消息结构)
    5.4. [核心消息交互流程](#54-核心消息交互流程)
6.  [插件系统规范](#6-插件系统规范)
    6.1. [设计概述](#61-设计概述)
    6.2. [核心接口规范](#62-核心接口规范)
    6.3. [设计案例：远程摄像头监控插件](#63-设计案例远程摄像头监控插件)
7.  [安全加固策略](#7-安全加固策略)
    7.1. [客户端加固](#71-客户端加固)
    7.2. [服务器端加固](#72-服务器端加固)
    7.3. [内存隐匿技术](#73-内存隐匿技术)
8.  [结论](#8-结论)
    8.1. [关键优势总结](#81-关键优势总结)
    8.2. [主要挑战与应对](#82-主要挑战与应对)

---

### 1. 引言

#### 1.1. 项目目标

本文档旨在为“Aegis”项目提供一份全面、统一的软件架构与技术栈设计方案。Aegis项目的核心目标是构建一个高度隐蔽、功能灵活、具备韧性的远程监控与响应系统。该系统由一个基于Node.js的中央服务器（C2）和一个基于Go语言的轻量级客户端（Agent）组成，旨在对抗性环境中实现稳定、安全的远程操作。

#### 1.2. 核心设计原则

整个系统的设计遵循以下三大原则：

-   隐匿性 (Stealth): 所有客户端组件设计为“无文件”（Fileless）执行，即在内存中运行，避免磁盘写入。通过代码混淆、流量加密和反分析技术，最大限度地规避终端检测与响应（EDR）及防病毒（AV）系统的侦测。
-   模块化 (Modularity): 核心功能通过动态插件系统实现。客户端本身仅作为轻量级加载器（Loader），所有具体操作（如信息收集、持久化）均由服务器按需下发的插件完成。这极大地增强了系统的灵活性和可扩展性。
-   韧性 (Resilience): 客户端与服务器具备强大的自恢复能力，能够处理网络中断、插件崩溃等异常情况，并能安全地自我清理，不留痕迹。

#### 1.3. 报告结构

本报告将从高层架构入手，逐层深入到各个核心组件的具体设计，内容涵盖：

-   总体架构: 描绘系统各组件及其交互关系。
-   服务器与客户端架构: 分别详述其内部模块设计、技术选型和实现细节。
-   通信协议: 定义客户端与服务器之间安全、可靠的通信标准。
-   插件系统: 规范插件的开发、加载和执行模型。
-   安全加固: 提供一套可操作的安全增强策略，以提升系统在真实环境中的生存能力。

---

### 2. 系统总体架构

#### 2.1. 架构概览

Aegis采用经典的指挥与控制（C2）架构模式，强调组件的职责分离、安全通信和动态功能扩展。

[图1：Aegis系统高层架构图]

#### 2.2. 核心组件职责

| 组件 | 技术栈 | 核心职责 |
| :--- | :--- | :--- |
| 服务器 (Server) | Node.js | 系统的指挥中心。负责管理所有客户端连接、鉴权、分发插件、处理上报数据，并为操作员提供管理接口。 |
| 客户端 (Client) | Go | 部署在目标系统上的轻量级代理。负责建立和维持与服务器的隐蔽通信，并作为“无文件”插件的内存加载器和执行环境。 |
| 通信信道 | WebSocket + XOR | 客户端与服务器之间的加密双向通信链路。WebSocket提供低延迟的持久连接，XOR加密层用于基础流量混淆。 |
| 插件库 (Plugin Lib) | 二进制文件 | 存储在服务器上的功能模块集合。每个插件都是一个独立的二进制文件，实现特定任务（如截屏、键盘记录等）。 |
| 操作员界面 (UI) | Web (React/Vue) | 操作员与Aegis系统交互的图形化界面，通过RESTful API与服务器通信（*UI本身不在本文档设计范围内*）。 |

#### 2.3. 数据与控制流

1.  植入与启动: Go客户端（通常以Shellcode形式）在目标主机上被执行。
2.  连接与注册: 客户端启动后，主动与服务器建立加密的WebSocket连接，并发送`register`消息进行注册，获取唯一ID。
3.  心跳与任务: 客户端定期发送`heartbeat`维持会话。服务器根据操作员指令，通过`task`消息向客户端下发任务，任务内容通常包含要执行的插件二进制码和参数。
4.  插件执行: 客户端接收并解密插件，将其加载到内存中执行。
5.  数据回传: 插件在执行过程中，通过客户端提供的核心API，将收集到的数据（日志、文件、流数据等）通过`data`或`stream`消息回传给服务器。
6.  数据处理: 服务器接收数据，进行处理、存储，并在操作员界面上展示。

---

### 3. 服务器端架构设计 (Node.js)

#### 3.1. 技术栈与目标

服务器端基于Node.js构建，利用其异步I/O模型和事件驱动特性，实现对大量并发客户端连接的高效管理。目标是打造一个稳定、可扩展且安全的C2后端。

#### 3.2. 核心模块与交互

服务器采用模块化设计，各模块职责清晰，通过标准接口通信。

[图2：服务器核心模块交互图]

#### 3.3. 模块详解

*   WebSocket 管理器 (WebSocketManager):
    *   职责: 作为服务器与所有客户端通信的唯一入口。
    *   功能:
        *   处理WebSocket连接请求，进行初步身份认证。
        *   实现心跳机制（Ping/Pong）以检测客户端存活状态。
        *   作为消息网关，对所有出入站消息进行XOR加解密，并根据消息类型路由到其他模块。

*   插件管理器 (PluginManager):
    *   职责: 负责插件的全生命周期管理。
    *   功能:
        *   使用MongoDB GridFS或对象存储来存储插件二进制文件及其元数据（名称、版本、哈希值）。
        *   支持插件版本控制，允许操作员部署特定版本的插件。
        *   响应客户端的插件请求，将插件二进制文件加密后下发。

*   客户端管理器 (ApiClientManager):
    *   职责: 跟踪、管理和操作所有已连接的客户端。
    *   功能:
        *   为每个客户端分配唯一UUID，并实时维护其状态（在线/离线、IP、操作系统等）。
        *   接收来自`OperatorApi`的任务指令，封装后通过`WebSocketManager`发送给目标客户端。
        *   处理客户端的心跳和状态报告，更新数据库记录。

*   操作员接口 (OperatorApi):
    *   职责: 为前端操作界面提供一套标准化的RESTful API。
    *   功能:
        *   使用JWT对所有API请求进行认证和基于角色的访问控制（RBAC）。
        *   提供任务下发、客户端查询、插件管理（上传/删除）等接口。
        *   将HTTP请求转换为内部指令，调用相应模块执行。

#### 3.4. 数据处理与存储

根据数据类型的不同，采用灵活的存储策略。

| 数据类型 | 示例内容 | 存储机制 |
| :--- | :--- | :--- |
| 结构化日志 | 插件运行日志、系统事件 | 存储在MongoDB的专用集合中，并建立索引以便快速查询。 |
| 文件/截图 | 屏幕截图、下载的文件 | 存储在对象存储 (S3兼容) 或 MongoDB GridFS中，元数据记录在MongoDB。 |
| 实时流数据 | 摄像头视频帧 | 通过`DataProcessor`实时处理，可推送到专用媒体服务器或直接通过WebSocket代理给前端。 |

#### 3.5. Operator API 接口定义

所有API端点均受JWT保护。

| 端点 (Endpoint) | 方法 (Method) | 描述 (Description) |
| :--- | :--- | :--- |
| `POST /api/tasks` | POST | 向一个或多个客户端下发任务（如执行插件）。 |
| `GET /api/clients` | GET | 获取所有客户端的列表及状态。 |
| `GET /api/plugins` | GET | 获取可用的插件列表及其版本信息。 |
| `POST /api/plugins` | POST | 上传新的插件或插件的新版本。 |

---

### 4. 客户端架构设计 (Go)

#### 4.1. 设计哲学与目标

Go客户端（Agent）作为部署在目标系统上的执行单元，其设计严格遵循隐匿、轻量、模块化和韧性的原则。它是一个核心加载器，不包含任何业务逻辑。

#### 4.2. 启动与加载流程

客户端的启动是一个多阶段过程，以最大化其隐蔽性。

[图3：客户端启动与加载流程图]

*   阶段一: Shellcode 加载器 (Stage 1):
    1.  一段极小的、与位置无关的Shellcode被植入目标进程。
    2.  Shellcode在内存中申请一块可读、可写、可执行（RWX）的区域。
    3.  将自身携带的Go主程序二进制数据解密/解压到该内存区域。
    4.  计算Go程序在内存中的入口点，并跳转执行。

*   阶段二: Go 主程序初始化 (Stage 2):
    1.  Go运行时（runtime）初始化，设置GC、调度器等。
    2.  API动态解析：为规避静态分析，客户端在运行时通过遍历PEB和LDR，基于函数名的哈希值动态查找所需Windows API的地址，而不是静态导入。
    3.  建立至服务器的加密WebSocket连接，并发送`register`消息进行注册。
    4.  进入主事件循环，等待处理来自服务器的指令。

#### 4.3. 插件的内存执行模型

这是实现“无文件”操作的核心。

[图5：插件内存加载与执行模型图]

1.  插件接收: 客户端接收到服务器下发的`task`指令，其`payload`中包含Base64编码的插件二进制数据。
2.  内存准备: 使用动态解析的`VirtualAlloc` API申请一块新的RWX内存区域。
3.  加载执行:
    *   将解密后的插件二进制数据完整地复制到新分配的内存中。
    *   通过Go的`unsafe`和`syscall`包，将内存地址转换为函数指针。
    *   在一个新的、受`panic/recover`保护的goroutine中调用该函数指针，将执行权交给插件。`recover`机制可防止因插件崩溃而导致整个客户端退出。

#### 4.4. 核心运行时 API (CoreAPI)

为实现插件与加载器的解耦，加载器向插件提供一个标准化的`CoreAPI`接口，插件的入口函数会接收该接口的实例。

```go
// CoreAPI 定义了客户端加载器提供给插件的核心功能。
type CoreAPI interface {
    // Send 向C2服务器发送一个标准化的消息。
    Send(messageType string, payload interface{}) error

    // StartStream 初始化一个大数据流传输通道。
    StartStream(metadata interface{}) (Stream, error)

    // Log 是一个便捷的日志记录函数。
    Log(level string, message string)

    // InvokeWinAPI 安全地调用一个已动态解析的Windows API。
    InvokeWinAPI(hash uint32, params ...uintptr) (uintptr, error)
}
```
*(详见 第6章：插件系统规范)*

#### 4.5. 心跳与状态管理

-   连接维护: 如果连接断开，客户端将启动带指数退避算法的重连机制，避免在网络长时间中断时产生风暴式请求。
-   心跳包: 客户端以固定间隔（如30-60秒）向服务器发送`heartbeat`消息，报告其存活和当前状态（如`idle`或`busy`）。

---

### 5. 通信协议规范

#### 5.1. 协议概述

所有客户端与服务器之间的通信均基于WebSocket，并封装在一个自定义的加密与消息框架内。

[图4：客户端-服务器通信流程图]

#### 5.2. XOR 加密层

为混淆网络流量，所有WebSocket消息的载荷在传输前都经过一层XOR加密处理。

-   密钥: 使用一个在服务器和客户端静态硬编码的预共享密钥（PSK）。
-   过程: 将JSON消息序列化为字符串，然后对每个字节与密钥进行循环异或操作。接收方执行相同操作进行解密。

#### 5.3. 通用消息结构

所有消息均遵循统一的顶层JSON结构。

```json
{
  "type": "message_type_string",
  "payload": {},
  "meta": {
    "clientId": "client_unique_id_string",
    "timestamp": "ISO_8601_string"
  }
}
```

| 字段名 | 类型 | 描述 |
| :--- | :--- | :--- |
| `type` | `String` | 必需。消息类型的唯一标识符，用于路由。 |
| `payload` | `Object` | 必需。消息的核心载荷，其结构由`type`决定。 |
| `meta` | `Object` | 必需。包含消息元数据，如`clientId`和`timestamp`。 |

#### 5.4. 核心消息交互流程

| 流程 | 消息类型 (`type`) | 方向 | `payload` 核心字段 | 描述 |
| :--- | :--- | :--- | :--- | :--- |
| 注册 | `register` | C → S | `osInfo`, `arch`, `hostname` | 客户端首次连接时发送，用于注册自身。 |
| | `register_ack` | S → C | `clientId` | 服务器响应，返回分配给客户端的唯一ID。 |
| 心跳 | `heartbeat` | C → S | `status` (`idle`/`busy`) | 客户端定期发送以表明存活状态。 |
| 任务下发 | `task` | S → C | `taskId`, `pluginId`, `pluginCode`, `args` | 服务器下发指令，通常包含插件的Base64编码二进制码。 |
| | `task_ack` | C → S | `taskId`, `status` | 客户端确认收到任务。 |
| 数据上报 | `data` | C → S | `taskId`, `dataType`, `content` | 插件通过此消息上报收集到的数据（Base64编码）。 |
| 大数据流 | `stream_start`, `stream_data`, `stream_end` | C → S | `streamId`, `chunkIndex`, `chunkData` | 用于传输大文件或实时视频流的分片协议。 |

---

### 6. 插件系统规范

#### 6.1. 设计概述

Aegis的插件系统是其功能扩展的核心。所有插件都被设计为在内存中运行的独立二进制模块，通过一组标准接口与客户端加载器交互。

#### 6.2. 核心接口规范

*   插件接口 (`Plugin`): 所有插件都必须实现此接口，它定义了插件的生命周期方法。
    ```go
    type Plugin interface {
        Init(coreAPI CoreAPI) error // 初始化
        Run(args []byte) error      // 执行
        Stop() error                // 停止
        Name() string               // 返回名称
    }
    ```
    插件必须导出一个名为`NewPlugin()`的函数，该函数返回一个实现了`Plugin`接口的实例。

*   客户端核心API (`CoreAPI`): 由加载器实现并提供给插件，用于与外部世界安全交互。详细定义见 4.4节。

#### 6.3. 设计案例：远程摄像头监控插件

-   目标: 响应服务器指令，捕获摄像头视频并以MJPEG格式流式回传。
-   工作流程:
    1.  插件的`Run`方法被调用。
    2.  通过`coreAPI.StartStream()`初始化一个视频流传输通道。
    3.  使用`coreAPI.InvokeWinAPI()`动态调用Windows Media Foundation (WMF) API来初始化摄像头。
    4.  进入循环，捕获视频帧，使用纯Go库将其编码为JPEG。
    5.  将每一帧JPEG数据通过`stream.Write()`写入流中。
    6.  当`Stop`方法被调用时，停止捕获，释放摄像头资源，并调用`stream.Close()`关闭流。

---

### 7. 安全加固策略

#### 7.1. 客户端加固

-   代码混淆 (`garble`):
    *   策略: 使用`garble`工具对Go编译产物进行深度混淆，包括混淆包/函数/类型名、加密字符串字面量、平坦化控制流。
    *   命令示例: `garble -tiny -literals build -ldflags="-s -w -H=windowsgui"`

-   反调试技术:
    *   策略: 组合使用多种技术主动检测调试器，如调用`IsDebuggerPresent` API、检查PEB中的`BeingDebugged`标志位、以及时间差检测。检测到调试器时，应立即静默退出。

-   Shellcode 生成 (`go-donut`):
    *   策略: 使用`go-donut`等工具将最终编译好的PE文件转换为与位置无关的Shellcode。`donut`会内嵌一个微型加载器，负责在内存中正确解析和加载PE文件，处理导入表和重定位，确保Go运行时环境正常启动。

#### 7.2. 服务器端加固

-   代码混淆 (`javascript-obfuscator`):
    *   策略: 使用`javascript-obfuscator`对部署的Node.js代码进行多层混淆，包括控制流平坦化、字符串数组化、自我防御和反调试。

-   运行时保护:
    *   环境变量加密: 使用`dotenv-vault`等工具加密存储敏感配置（如数据库密码、API密钥），在运行时使用主密钥解密到内存。
    *   依赖项安全审计: 在CI/CD流程中集成`npm audit`或Snyk/Dependabot，持续扫描第三方库的已知漏洞。

#### 7.3. 内存隐匿技术

-   API 哈希: 不在代码中硬编码敏感的WinAPI函数名。在启动时动态计算其哈希值，并通过遍历系统DLL导出表来匹配和定位函数地址。
-   即时解密与内存擦除: 敏感数据（如通信密钥、C2地址）在内存中以加密形式保存。仅在函数作用域内解密使用，使用完毕后立即用零覆盖明文所在的内存区域，以对抗内存扫描。

---

### 8. 结论

#### 8.1. 关键优势总结

本设计方案为Aegis项目构建了一个兼具隐蔽性、灵活性和韧性的坚实基础。其关键优势在于：

-   高度模块化: 基于插件的架构允许在不修改核心客户端的情况下快速迭代和扩展功能。
-   卓越的隐匿性: “无文件”内存执行模型、API哈希、代码混淆和流量加密共同构成了一套深度防御体系，显著增加了被检测和分析的难度。
-   强大的灵活性: Go语言的跨平台编译能力和Node.js的高并发处理能力为未来向不同操作系统扩展和应对大规模部署提供了可能。

#### 8.2. 主要挑战与应对

尽管架构设计稳健，但在实现过程中仍面临以下挑战：

-   Shellcode工程复杂性: 将包含Go运行时的复杂程序稳定地转换为可靠的Shellcode，需要对PE结构和内存加载有深入理解。应对策略是优先依赖成熟的第三方工具（如`donut`），并进行充分测试。
-   持续的攻防对抗: EDR和AV技术在不断进化，今天的有效规避技术明天可能失效。应对策略是建立一个持续研究和迭代的安全机制，定期更新混淆、加密和加载技术。
-   C2服务器安全: 作为系统的中枢，服务器是最高价值目标。应对策略是除了代码加固外，还必须遵循严格的部署安全最佳实践，包括网络隔离、最小权限原则和入侵检测系统。